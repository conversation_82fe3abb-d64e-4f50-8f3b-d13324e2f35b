<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import type { LoadOptionProgressCallback, ProgressInfo, ProgressStatusInfo } from '@xsai-transformers/shared/types';
	import { createVLMWorker, type VLMWorker } from '$lib/vlm';
	import workerURL from '$lib/worker?worker&url';

	// --- 响应式变量 ---

	// 元素引用
	let videoScreen: HTMLVideoElement;
	let captureCanvas: HTMLCanvasElement;

	// 状态管理
	let isSupported = true;
	let isWebGPUSupported = true;
	let permissionGranted = false;
	let loaded = false;
	let isProcessing = false;
	let isModelLoading = false;
	let isWebGPULoading = false;
	let isDark = false; // 暗黑模式
	let showSettings = false; // 控制设置面板的显示

	// AI 相关
	let instructionText = 'In one sentence, what do you see?';
	let responseText = '';
	let vlmWorker: VLMWorker | null = null;

	// 性能控制
	let scale = 0.3; // 图像缩放
	let maxImageSize = 224; // 最大图像尺寸
	let processingInterval = 2000; // 处理间隔 (ms)

	// 性能监控
	let fpsCounter = 0;
	let lastFrameTime = 0;
	let processingTime = 0;

	// 模型加载进度
	let overallProgress = 0;
	let overallTotal = 0;
	let loadingItems: ProgressInfo[] = [];
	const loadingItemsSet = new Set<string>();


	// 摄像头与媒体设备
	let stream: MediaStream | null = null;
	let videoInputs: MediaDeviceInfo[] = [];
	let selectedVideoSourceDeviceId: string | undefined = undefined;

	// --- 生命周期 ---

	onMount(async () => {
		// 检查 WebGPU 和媒体设备支持
		isWebGPUSupported = !!navigator.gpu;
		isSupported = !!navigator.mediaDevices;

		if (!isWebGPUSupported || !isSupported) {
			return;
		}

		// 获取设备列表并请求权限
		await setupDevices();

		vlmWorker = createVLMWorker({ baseURL: workerURL });
	});

	onDestroy(() => {
		if (stream) {
			stream.getTracks().forEach((track) => track.stop());
		}
		vlmWorker?.dispose();
	});

	// --- 核心函数 ---

	async function setupDevices() {
		try {
			// 请求权限以触发设备列表的填充
			const tempStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
			permissionGranted = true;
			tempStream.getTracks().forEach(track => track.stop()); // 关闭临时流

			// 获取视频设备列表
			const devices = await navigator.mediaDevices.enumerateDevices();
			videoInputs = devices.filter(device => device.kind === 'videoinput');
			if (videoInputs.length > 0) {
				selectedVideoSourceDeviceId = videoInputs[0].deviceId;
				await startCamera();
			}
		} catch (err) {
			console.error("Error accessing media devices.", err);
			permissionGranted = false;
		}
	}

	async function startCamera() {
		if (stream) {
			stream.getTracks().forEach((track) => track.stop());
		}
		try {
			const constraints = {
				video: { deviceId: selectedVideoSourceDeviceId ? { exact: selectedVideoSourceDeviceId } : undefined },
				audio: false
			};
			stream = await navigator.mediaDevices.getUserMedia(constraints);
			if (videoScreen) {
				videoScreen.srcObject = stream;
			}
		} catch (err) {
			console.error("Error starting camera:", err);
		}
	}

	const onProgress: LoadOptionProgressCallback = async (progress) => {
		if (progress.status === 'initiate') {
			if (loadingItemsSet.has(progress.file)) {
				return;
			}
			loadingItemsSet.add(progress.file);
			loadingItems = [...loadingItems, progress];
		} else if (progress.status === 'progress' || progress.status === 'done') {
			const itemIndex = loadingItems.findIndex((item: any) => item.file === progress.file);
			if (itemIndex >= 0) {
				loadingItems[itemIndex] = progress;
				loadingItems = [...loadingItems];
			}

			let newTotalSize = 0;
			let newLoadedSize = 0;
			for (const item of loadingItems) {
				if ('total' in item && item.total) {
					newTotalSize += item.total;
					if ('loaded' in item && item.loaded) {
						newLoadedSize += item.loaded;
					}
				}
			}
			overallTotal = newTotalSize;
			if (newTotalSize > 0) {
				const progress = (newLoadedSize / newTotalSize) * 100;
				// Cap progress at 99.9% to prevent premature completion display.
				// The final 100% is set in handleStart after await.
				overallProgress = Math.min(progress, 99.9);
			}
		}
	};

	function captureImage() {
		if (!stream || !videoScreen?.videoWidth || !captureCanvas) {
			return null;
		}

		const originalWidth = videoScreen.videoWidth;
		const originalHeight = videoScreen.videoHeight;
		const scaledWidth = Math.round(originalWidth * scale);
		const scaledHeight = Math.round(originalHeight * scale);
		const aspectRatio = originalWidth / originalHeight;

		let finalWidth = scaledWidth;
		let finalHeight = scaledHeight;

		if (Math.max(scaledWidth, scaledHeight) > maxImageSize) {
			if (scaledWidth > scaledHeight) {
				finalWidth = maxImageSize;
				finalHeight = Math.round(maxImageSize / aspectRatio);
			} else {
				finalHeight = maxImageSize;
				finalWidth = Math.round(maxImageSize * aspectRatio);
			}
		}

		captureCanvas.width = finalWidth;
		captureCanvas.height = finalHeight;
		const context = captureCanvas.getContext('2d', { willReadFrequently: true });
		if (!context) return null;

		context.drawImage(videoScreen, 0, 0, finalWidth, finalHeight);
		const frame = context.getImageData(0, 0, finalWidth, finalHeight);

		return {
			imageBuffer: frame.data,
			imageWidth: frame.width,
			imageHeight: frame.height,
			channels: 4 as const
		};
	}

	async function sendData() {
		if (!isProcessing || !vlmWorker) return;

		const rawImg = captureImage();
		if (!rawImg) {
			responseText = 'Capture failed';
			return;
		}

		try {
			const startTime = performance.now();
			const response = await vlmWorker.process({
				instruction: instructionText,
				imageBuffer: rawImg.imageBuffer,
				imageWidth: rawImg.imageWidth,
				imageHeight: rawImg.imageHeight,
				channels: rawImg.channels
			});
			const endTime = performance.now();

			processingTime = Math.round(endTime - startTime);
			if (lastFrameTime) {
				fpsCounter = Math.round(1000 / (endTime - lastFrameTime) * 100) / 100;
			}
			lastFrameTime = endTime;
			responseText = response ?? '';

		} catch (e) {
			console.error(e);
			responseText = `Error: ${e instanceof Error ? e.message : 'Unknown error'}`;
		}
	}

	let animationFrameId: number | null = null;
	let lastProcessTime = 0;

	function processingLoop() {
		if (!isProcessing) {
			if (animationFrameId !== null) cancelAnimationFrame(animationFrameId);
			return;
		}

		const now = performance.now();
		if (now - lastProcessTime >= processingInterval) {
			lastProcessTime = now;
			sendData().finally(() => {
				if (isProcessing) animationFrameId = requestAnimationFrame(processingLoop);
			});
		} else {
			if (isProcessing) animationFrameId = requestAnimationFrame(processingLoop);
		}
	}

	async function handleStart() {
		if (!stream) {
			responseText = 'Camera not available. Cannot start.';
			console.warn('Camera not available. Please grant permission first.');
			return;
		}

		isProcessing = true;

		if (!loaded) {
			// Reset progress state and show the progress bar
			overallProgress = 0;
			overallTotal = 0;
			loadingItems = [];
			loadingItemsSet.clear();
			isModelLoading = true; // Show progress bar immediately
			isWebGPULoading = true;

			await vlmWorker?.load({ onProgress });

			isWebGPULoading = false;

			// After loading is fully complete, ensure progress is 100% and then hide bar
			overallProgress = 100;
			isModelLoading = false;

			if (!isProcessing) { // User might have cancelled during load
				responseText = '';
				return;
			}
			loaded = true;
		}

		responseText = '...';
		processingLoop();
	}

	function handleStop() {
		isProcessing = false;
		isModelLoading = false; // Also reset loading state on stop
		if (responseText === '...') responseText = '';
	}

	function handleClick() {
		if (isProcessing) {
			handleStop();
		} else {
			handleStart();
		}
	}

	// --- Svelte 反应性 ---
	$: if (selectedVideoSourceDeviceId) {
		startCamera();
	}
</script>

<svelte:head>
	<title>SmolVLM Realtime WebGPU (Svelte)</title>
</svelte:head>

<div class="p-0 sm:p-4 max-h-dvh max-w-dvw relative h-full w-full">
	<div class="relative z-0 h-full w-full overflow-hidden rounded-none sm:rounded-3xl shadow-md flex items-center justify-center">
		<!-- 左上角信息 -->
		<div class="absolute left-0 top-0 z-10 p-2 sm:p-4 flex gap-2">
			<div class="bg-white dark:bg-neutral-900 text-black dark:text-white text-xs sm:text-base border border-neutral-400/40 dark:border-neutral-500/50 rounded-xl p-2 sm:rounded-2xl sm:px-3 sm:py-2 transition-all duration-300 ease-in-out">
				SmolVLM Realtime WebGPU (Svelte)
			</div>
		</div>

		<!-- 性能监控 -->
		<div class="absolute right-4 top-4 z-10 bg-white/80 dark:bg-neutral-900/80 text-black dark:text-white text-xs sm:text-base border border-neutral-400/40 dark:border-neutral-500/50 rounded-xl p-2 sm:rounded-2xl sm:px-3 sm:py-2 transition-all duration-300 ease-in-out flex gap-2">
			<span>FPS: <span class="font-mono">{fpsCounter}</span></span>
			<span>Time: <span class="font-mono">{processingTime} ms</span></span>
		</div>

		<!-- 控制面板 -->
		{#if showSettings}
			<div class="grid-cols-[0.2fr_0.4fr_1fr] absolute bottom-16 right-4 z-10 grid items-center gap-x-2 gap-y-1 text-sm min-w-[280px] bg-neutral-500/40 dark:bg-neutral-900/70 text-white/98 dark:text-neutral-100/90 border border-neutral-400/40 dark:border-neutral-500/50 rounded-xl p-2 sm:rounded-2xl sm:px-3 sm:pb-1 sm:pt-2 transition-all duration-300 ease-in-out backdrop-blur-lg">
				<div>Scale:</div>
				<label for="scale" class="w-[90px] flex items-center gap-2">
					<input type="range" bind:value={scale} min={0.1} max={1.0} step={0.1} disabled={isProcessing} class="flex-1" />
				</label>
				<div class="text-right font-mono">{scale.toFixed(1)}</div>

				<div>Max Size:</div>
				<label for="max-size" class="w-[90px] flex items-center gap-2">
					<input type="range" bind:value={maxImageSize} min={128} max={512} step={32} disabled={isProcessing} class="flex-1" />
				</label>
				<div class="text-right font-mono">{maxImageSize}</div>

				<div>Interval:</div>
				<label for="interval" class="w-[90px] flex items-center gap-2">
					<input type="range" bind:value={processingInterval} min={500} max={5000} step={250} class="flex-1" />
				</label>
				<div class="text-right font-mono">{(processingInterval / 1000).toFixed(1)}s</div>

				<div class="min-w-20">Ask:</div>
				<label for="instruction" class="col-span-2 w-full flex items-center gap-2">
					<input type="text" bind:value={instructionText} placeholder="What do you see?" disabled={isProcessing} class="w-full flex-1 rounded-lg px-2 py-1 text-nowrap text-sm outline-none bg-neutral-700/50 dark:bg-neutral-950 focus:bg-neutral-700/50 dark:focus:bg-neutral-900 border-2 border-solid border-neutral-500/50 dark:border-neutral-900 focus:border-neutral-400 dark:focus:border-neutral-500 transition-all duration-200 ease-in-out disabled:cursor-not-allowed text-white disabled:text-neutral-400 dark:disabled:text-neutral-600 shadow-sm" />
				</label>
			</div>
		{/if}

		<!-- 开始/停止按钮 -->
		<button on:click={handleClick} class="absolute bottom-2 left-2 sm:bottom-4 sm:left-4 z-10 h-full max-h-8 sm:max-h-10 px-2 sm:px-3 sm:py-2 flex items-center rounded-full backdrop-blur-lg text-white/98 dark:text-neutral-100/90 border border-neutral-400/40 dark:border-neutral-500/50 transition-all duration-300 ease-in-out {isProcessing ? 'bg-red-700/60 dark:bg-red-900/90 hover:bg-red-800/60 dark:hover:bg-red-900/90' : 'bg-green-700/60 dark:bg-green-900/90 hover:bg-green-800/60 dark:hover:bg-green-900/90'}">
			{#if isProcessing}
				Stop
			{:else if isWebGPULoading || isModelLoading}
				<div class="i-svg-spinners:6-dots-rotate size-4" />
			{:else}
				Start
			{/if}
		</button>


		<!-- Info Box: Progress or Response -->
		{#if isModelLoading || responseText}
			<div class="absolute bottom-4 left-1/2 -translate-x-1/2 z-10 w-full max-w-lg px-3 py-2 bg-neutral-500/40 dark:bg-neutral-900/70 border border-neutral-400/40 dark:border-neutral-500/50 rounded-2xl backdrop-blur-lg transition-all duration-300 ease-in-out">
				{#if isModelLoading}
					<!-- Progress Bar Content -->
					<div class="flex items-center gap-3">
						<div class="flex-1 bg-gray-300 rounded-full h-2.5 dark:bg-gray-700">
							<div class="bg-gray-500 dark:bg-gray-400 h-2.5 rounded-full" style="width: {overallProgress}%"></div>
						</div>
						<p class="text-neutral-800 dark:text-neutral-300 text-sm font-mono">{Math.round(overallProgress)}%</p>
					</div>
				{:else if responseText}
					<!-- Response Text Content -->
					<p class="text-white/98 dark:text-neutral-100/90 text-xs sm:text-xl text-center">
						{responseText}
					</p>
				{/if}
			</div>
		{/if}

		<!-- 覆盖层: 能力/权限检查 -->
		{#if !isSupported}
			<div class="absolute inset-0 z-20 flex flex-col items-center justify-center gap-3 bg-neutral-50/20">
				<div class="text-neutral-700 dark:text-neutral-300 text-4xl font-semibold">Not Supported</div>
				<div class="max-w-[50%] text-neutral-800 dark:text-neutral-200 text-2xl text-center">Browser does not support video camera. Please use a supported browser.</div>
			</div>
		{:else if !isWebGPUSupported}
			<div class="absolute inset-0 z-20 flex flex-col items-center justify-center gap-3 bg-neutral-50/20">
				<div class="text-neutral-700 dark:text-neutral-300 text-4xl font-semibold">Not Supported</div>
				<div class="max-w-[50%] text-neutral-800 dark:text-neutral-200 text-2xl text-center">Browser does not support WebGPU. Please use a supported browser.</div>
			</div>
		{:else if !permissionGranted}
			<div class="absolute inset-0 z-20 flex flex-col items-center justify-center gap-3 bg-orange-50/20 dark:bg-orange-900/10">
				<div class="text-orange-700 text-4xl font-semibold">Warning</div>
				<div class="max-w-[50%] text-orange-600 dark:text-orange-400 text-2xl text-center">Permission not granted. Please grant permission first.</div>
			</div>
		{:else}
			<div class="relative h-5/6 w-5/6 overflow-hidden rounded-2xl shadow-lg">
				<video bind:this={videoScreen} autoplay muted playsinline class="h-full w-full object-cover"></video>
			</div>
			<canvas bind:this={captureCanvas} class="hidden"></canvas>
		{/if}

		<!-- 右下角设备选择和主题切换 -->
		<div class="absolute bottom-0 right-0 z-10 h-full max-h-12 sm:max-h-18 p-2 sm:p-4 flex items-center gap-1 sm:gap-2">
			<button on:click={() => showSettings = !showSettings} class="h-full aspect-square flex items-center justify-center rounded-full cursor-pointer bg-neutral-500/40 hover:bg-neutral-600/40 dark:bg-neutral-900/70 hover:dark:bg-neutral-900/60 text-white/98 dark:text-neutral-100/90 border border-neutral-400/40 dark:border-neutral-500/50 outline-none shadow-none hover:shadow-lg backdrop-blur-lg transition-all duration-300 ease-in-out">
				<svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
					<path d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"/>
				</svg>
			</button>
			<select bind:value={selectedVideoSourceDeviceId} class="h-full px-2 sm:px-3 cursor-pointer rounded-full bg-neutral-500/40 hover:bg-neutral-600/40 dark:bg-neutral-900/70 hover:dark:bg-neutral-900/60 text-white/98 dark:text-neutral-100/90 text-xs sm:text-base border border-neutral-400/40 dark:border-neutral-500/50 outline-none shadow-none hover:shadow-lg backdrop-blur-lg transition-all duration-300 ease-in-out">
				{#each videoInputs as device}
					<option value={device.deviceId} class="text-xs sm:text-sm">{device.label}</option>
				{/each}
			</select>
			<button on:click={() => isDark = !isDark} class="h-full aspect-square flex items-center justify-center rounded-full cursor-pointer bg-neutral-500/40 hover:bg-neutral-600/40 dark:bg-neutral-900/70 hover:dark:bg-neutral-900/60 text-white/98 dark:text-neutral-100/90 border border-neutral-400/40 dark:border-neutral-500/50 outline-none shadow-none hover:shadow-lg backdrop-blur-lg transition-all duration-300 ease-in-out">
				<!-- Sun/Moon Icon -->
				{#if isDark}
					<svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.25a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0V3a.75.75 0 0 1 .75-.75ZM7.5 12a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM18.697 5.303a.75.75 0 0 1 1.06-1.06l1.5 1.5a.75.75 0 0 1-1.06 1.06l-1.5-1.5ZM21.75 12a.75.75 0 0 1-.75.75h-2.25a.75.75 0 0 1 0-1.5h2.25a.75.75 0 0 1 .75.75ZM18.697 18.697a.75.75 0 0 1-1.06 1.06l-1.5-1.5a.75.75 0 1 1 1.06-1.06l1.5 1.5ZM12 18.75a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0v-2.25a.75.75 0 0 1 .75-.75ZM5.303 18.697a.75.75 0 0 1-1.06-1.06l1.5-1.5a.75.75 0 0 1 1.06 1.06l-1.5 1.5ZM2.25 12a.75.75 0 0 1 .75-.75h2.25a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1-.75-.75ZM5.303 5.303a.75.75 0 0 1 1.06 1.06l-1.5 1.5a.75.75 0 1 1-1.06-1.06l1.5-1.5Z"/></svg>
				{:else}
					<svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path fill-rule="evenodd" d="M9.528 1.718a.75.75 0 0 1 .162.819A8.97 8.97 0 0 0 9 6a9 9 0 0 0 9 9 8.97 8.97 0 0 0 3.463-.69a.75.75 0 0 1 .981.981A10.503 10.503 0 0 1 12 22.5C6.201 22.5 1.5 17.799 1.5 12c0-4.243 2.52-7.938 6.3-9.662a.75.75 0 0 1 .728.38Z" clip-rule="evenodd"/></svg>
				{/if}
			</button>
		</div>
	</div>
</div>

<style lang="postcss">
	/* Global styles have been moved to app.css */
	/* Svelte transition class for fade effect */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.3s ease-in-out;
	}
	.fade-enter-from,
	.fade-leave-to {
		opacity: 0.5;
	}
	.fade-enter-to,
	.fade-leave-from {
		opacity: 1;
	}
</style>
